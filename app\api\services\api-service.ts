// ============================================================================
// API SERVICE
// ============================================================================

import { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import axiosInstance from './axiosinstance';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  timestamp?: string;
}

/**
 * API error details
 */
export interface ApiErrorDetails {
  status: number;
  message: string;
  code?: string;
  details?: any;
}

// ============================================================================
// API SERVICE CLASS
// ============================================================================

class ApiService {
  /**
   * Perform GET request
   * @param url - API endpoint URL
   * @param config - Optional Axios request configuration
   * @returns Promise<T> - Response data
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      console.log(`🔍 GET request to: ${url}`);
      const response: AxiosResponse<T> = await axiosInstance.get(url, config);
      console.log(`✅ GET request successful: ${url}`);
      return response.data;
    } catch (error: unknown) {
      console.error(`❌ GET request failed: ${url}`, error);
      this.handleError(error);
    }
  }

  /**
   * Perform POST request
   * @param url - API endpoint URL
   * @param data - Request payload
   * @param config - Optional Axios request configuration
   * @returns Promise<T> - Response data
   */
  async post<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      console.log(`📤 POST request to: ${url}`, data ? 'with payload' : 'without payload');
      const response: AxiosResponse<T> = await axiosInstance.post(url, data, config);
      console.log(`✅ POST request successful: ${url}`);
      return response.data;
    } catch (error: unknown) {
      console.error(`❌ POST request failed: ${url}`, error);
      this.handleError(error);
    }
  }

  /**
   * Perform PUT request
   * @param url - API endpoint URL
   * @param data - Request payload
   * @param config - Optional Axios request configuration
   * @returns Promise<T> - Response data
   */
  async put<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      console.log(`📝 PUT request to: ${url}`, data ? 'with payload' : 'without payload');
      const response: AxiosResponse<T> = await axiosInstance.put(url, data, config);
      console.log(`✅ PUT request successful: ${url}`);
      return response.data;
    } catch (error: unknown) {
      console.error(`❌ PUT request failed: ${url}`, error);
      this.handleError(error);
    }
  }

  /**
   * Perform DELETE request
   * @param url - API endpoint URL
   * @param config - Optional Axios request configuration
   * @returns Promise<T> - Response data
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      console.log(`🗑️ DELETE request to: ${url}`);
      const response: AxiosResponse<T> = await axiosInstance.delete(url, config);
      console.log(`✅ DELETE request successful: ${url}`);
      return response.data;
    } catch (error: unknown) {
      console.error(`❌ DELETE request failed: ${url}`, error);
      this.handleError(error);
    }
  }

  async postFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async putFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  // 👇 Safe, typed error handling
  private handleError(error: unknown): never {
    if (error instanceof AxiosError) {
      console.error('API Error:', error.response?.data || error.message);
      throw error;
    }

    // Unknown type fallback
    console.error('Unexpected error:', error);
    throw new Error('An unexpected error occurred');
  }
}

const apiService = new ApiService();
export default apiService;
