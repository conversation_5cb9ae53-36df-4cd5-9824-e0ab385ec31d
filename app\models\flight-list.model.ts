// ============================================================================
// ROUND TRIP DOMESTIC FLIGHT MODELS
// ============================================================================

/**
 * API response structure for round trip domestic flights
 */
export interface FlightApiResponseRoundTripDomestic {
  searchResult: SearchResult;
  status: ApiStatus;
}

/**
 * Standard API status response
 */
export interface ApiStatus {
  success: boolean;
  httpStatus: number;
}

/**
 * Search result wrapper for domestic flights
 */
export interface SearchResult {
  tripInfos: TripInfos;
}

/**
 * Trip information container for domestic round trips
 */
export interface TripInfos {
  RETURN: FlightOption[];
}

/**
 * Individual flight option for domestic flights
 */
export interface FlightOption {
  sI: SegmentInfo[];                    // Segment Information array
  totalPriceList: FareOption[];         // Available fare options
  airFlowType: string;                  // Flight flow type
}

/**
 * Flight segment information for domestic flights
 */
export interface SegmentInfo {
  id: string;                           // Segment unique identifier
  fD: FlightDetails;                    // Flight details
  stops: number;                        // Number of stops
  so: any[];                           // Stop over airports
  duration: number;                     // Flight duration in minutes
  da: AirportInfo;                     // Departure airport
  aa: AirportInfo;                     // Arrival airport
  dt: string;                          // Departure time
  at: string;                          // Arrival time
  iand: boolean;                       // Is arriving next day
  isRs: boolean;                       // Is return segment
  sN: number;                          // Segment number
}

/**
 * Flight details including airline and flight number
 */
export interface FlightDetails {
  aI: AirlineInfo;                     // Airline information
  fN: string;                          // Flight number
  eT: string;                          // Equipment type
}

/**
 * Airline information
 */
export interface AirlineInfo {
  code: string;                        // Airline code
  name: string;                        // Airline name
  isLcc: boolean;                      // Is Low Cost Carrier
}

/**
 * Airport information
 */
export interface AirportInfo {
  code: string;                        // Airport code
  name: string;                        // Airport name
  cityCode: string;                    // City code
  city: string;                        // City name
  country: string;                     // Country name
  countryCode: string;                 // Country code
  terminal: string;                    // Terminal information
}

/**
 * Fare option with pricing and details
 */
export interface FareOption {
  fd: FareDetailsByPaxType;            // Fare details by passenger type
  fareIdentifier: string;              // Unique fare identifier
  id: string;                          // Fare option ID
  msri: any[];                         // Meal service request information
  messages: any[];                     // Fare messages
  tai: TravelAncillaryInfo;            // Travel ancillary information
  icca: boolean;                       // Is cabin class available
}

/**
 * Fare details organized by passenger type
 */
export interface FareDetailsByPaxType {
  ADULT: PassengerFareDetails;         // Adult passenger fare details
  CHILD: PassengerFareDetails;         // Child passenger fare details
}

/**
 * Detailed fare information for a passenger type
 */
export interface PassengerFareDetails {
  fC: FareComponents;                  // Fare components
  afC: AdditionalFareCharges;          // Additional fare charges
  bI: BaggageInfo;                     // Baggage information
  isHB?: boolean;                      // Is hand baggage included
  rT: number;                          // Refundable type
  cc: string;                          // Cabin class
  cB: string;                          // Class of booking
  fB: string;                          // Fare basis
  mI: boolean;                         // Meal indicator
  tjFlexFareBenefit?: string;          // Flex fare benefits
}

/**
 * Fare components breakdown
 */
export interface FareComponents {
  BF: number;                          // Base fare
  NF: number;                          // Net fare
  TAF: number;                         // Taxes and fees
  TF: number;                          // Total fare
  FCMU?: number;                       // Fuel charge markup
  FCGST?: number;                      // Fuel charge GST
  FACF?: number;                       // Fuel and airport charges
  FC?: number;                         // Fuel charges
  FCMUGST?: number;                    // Fuel charge markup GST
}

/**
 * Additional fare charges
 */
export interface AdditionalFareCharges {
  TAF: TaxBreakdown;                   // Tax breakdown
}

/**
 * Tax breakdown details
 */
export interface TaxBreakdown {
  MFT: number;                         // Management fee tax
  OT: number;                          // Other taxes
  MF: number;                          // Management fee
  FTC?: number;                        // Fuel tax charges
}

/**
 * Baggage information
 */
export interface BaggageInfo {
  iB: string;                          // Included baggage
  cB: string;                          // Cabin baggage
}

/**
 * Travel ancillary information
 */
export interface TravelAncillaryInfo {
  tbi: {
    [segmentId: string]: SegmentBaggagePerPax[];
  };
}

/**
 * Segment baggage information per passenger type
 */
export interface SegmentBaggagePerPax {
  ADULT?: BaggageInfo;                 // Adult baggage info
  CHILD?: BaggageInfo;                 // Child baggage info
}




// ============================================================================
// INTERNATIONAL ROUND TRIP FLIGHT MODELS
// ============================================================================

/**
 * API response structure for international round trip flights
 */
export interface FlightApiResponseInternational {
  searchResult: SearchResultInternational;
  status: ApiStatusInternational;
}

/**
 * API status for international flights
 */
export interface ApiStatusInternational {
  success: boolean;
  httpStatus: number;
}

/**
 * Search result wrapper for international flights
 */
export interface SearchResultInternational {
  tripInfos: TripInfosInternational;
}

/**
 * Trip information container for international round trips
 */
export interface TripInfosInternational {
  COMBO: FlightOptionInternational[];   // Combined onward and return flights
}

/**
 * Individual flight option for international flights
 */
export interface FlightOptionInternational {
  sI: SegmentInfoInternational[];       // Segment Information array
  totalPriceList: FareOptionInternational[]; // Available fare options
  airFlowType: string;                  // Flight flow type
}

/**
 * Flight segment information for international flights
 */
export interface SegmentInfoInternational {
  id: string;                           // Segment unique identifier
  fD: FlightDetailsInternational;       // Flight details
  stops: number;                        // Number of stops
  so: any[];                           // Stop over airports
  duration: number;                     // Flight duration in minutes
  da: AirportInfoInternational;        // Departure airport
  aa: AirportInfoInternational;        // Arrival airport
  dt: string;                          // Departure time
  at: string;                          // Arrival time
  iand: boolean;                       // Is arriving next day
  isRs: boolean;                       // Is return segment
  sN: number;                          // Segment number
  oda?: string;                        // Optional departure airport
  oaa?: string;                        // Optional arrival airport
}

/**
 * Flight details for international flights
 */
export interface FlightDetailsInternational {
  aI: AirlineInfoInternational;        // Airline information
  fN: string;                          // Flight number
  eT: string;                          // Equipment type
}

/**
 * Airline information for international flights
 */
export interface AirlineInfoInternational {
  code: string;                        // Airline code
  name: string;                        // Airline name
  isLcc: boolean;                      // Is Low Cost Carrier
}

/**
 * Airport information for international flights
 */
export interface AirportInfoInternational {
  code: string;                        // Airport code
  name: string;                        // Airport name
  cityCode: string;                    // City code
  city: string;                        // City name
  country: string;                     // Country name
  countryCode: string;                 // Country code
  terminal: string;                    // Terminal information
}

/**
 * Fare option for international flights
 */
export interface FareOptionInternational {
  fd: FareDetailsByPaxTypeInternational; // Fare details by passenger type
  fareIdentifier: string;              // Unique fare identifier
  id: string;                          // Fare option ID
  msri: any[];                         // Meal service request information
  messages: any[];                     // Fare messages
  tai: TravelAncillaryInfoInternational; // Travel ancillary information
  icca: boolean;                       // Is cabin class available
}

/**
 * Fare details by passenger type for international flights
 */
export interface FareDetailsByPaxTypeInternational {
  ADULT: PassengerFareDetailsInternational;   // Adult passenger fare details
  CHILD?: PassengerFareDetailsInternational;  // Child passenger fare details (optional)
  INFANT?: PassengerFareDetailsInternational; // Infant passenger fare details (optional)
}

/**
 * Passenger fare details for international flights
 */
export interface PassengerFareDetailsInternational {
  fC: FareComponentsInternational;     // Fare components
  afC: AdditionalFareChargesInternational; // Additional fare charges
  bI: BaggageInfoInternational;        // Baggage information
  isHB?: boolean;                      // Is hand baggage included
  rT: number;                          // Refundable type
  cc: string;                          // Cabin class
  cB: string;                          // Class of booking
  fB: string;                          // Fare basis
  mI: boolean;                         // Meal indicator
  tjFlexFareBenefit?: string;          // Flex fare benefits
}

/**
 * Fare components for international flights
 */
export interface FareComponentsInternational {
  BF: number;                          // Base fare
  NF: number;                          // Net fare
  TAF: number;                         // Taxes and fees
  TF: number;                          // Total fare
  FCMU?: number;                       // Fuel charge markup
  FCGST?: number;                      // Fuel charge GST
  FACF?: number;                       // Fuel and airport charges
  FC?: number;                         // Fuel charges
  FCMUGST?: number;                    // Fuel charge markup GST
}

/**
 * Additional fare charges for international flights
 */
export interface AdditionalFareChargesInternational {
  TAF: TaxBreakdownInternational;      // Tax breakdown
}

/**
 * Tax breakdown for international flights
 */
export interface TaxBreakdownInternational {
  MFT: number;                         // Management fee tax
  OT: number;                          // Other taxes
  MF: number;                          // Management fee
  FTC?: number;                        // Fuel tax charges
}

/**
 * Baggage information for international flights
 */
export interface BaggageInfoInternational {
  iB?: string;                         // Included baggage (optional)
  cB: string;                          // Cabin baggage
}

/**
 * Travel ancillary information for international flights
 */
export interface TravelAncillaryInfoInternational {
  tbi: {
    [segmentId: string]: SegmentBaggagePerPaxInternational[];
  };
}

/**
 * Segment baggage per passenger for international flights
 */
export interface SegmentBaggagePerPaxInternational {
  ADULT?: BaggageInfoInternational;    // Adult baggage info
  CHILD?: BaggageInfoInternational;    // Child baggage info
}

// ============================================================================
// ONE WAY FLIGHT MODELS
// ============================================================================

/**
 * API response structure for one way flights
 */
export interface FlightApiResponseOneWay {
  tui: string;                          // Transaction unique identifier
  provider: string;                     // Provider name
  status: string;                       // Response status
  searchResult: SearchResultWrapper;    // Wrapped search result
}

/**
 * Search result wrapper for one way flights
 */
export interface SearchResultWrapper {
  searchResult: SearchResultOneWay;     // Actual search result
  status: ApiStatusOneWay;              // API status
  isComplete: boolean;                  // Is search complete
}

/**
 * API status for one way flights
 */
export interface ApiStatusOneWay {
  success: boolean;                     // Success flag
  httpStatus: number;                   // HTTP status code
}

/**
 * Search result for one way flights
 */
export interface SearchResultOneWay {
  tripInfos: TripInfosOneWay;          // Trip information
}

/**
 * Trip information container for one way flights
 */
export interface TripInfosOneWay {
  ONWARD: FlightOptionOneWay[];        // Onward flight options
  // RETURN would be here in a round-trip response using this schema
}

/**
 * Individual flight option for one way flights
 */
export interface FlightOptionOneWay {
  SegmentInformation: SegmentInformation[]; // Flight segments
  totalPriceList: FareOptionOneWay[];   // Available fare options
  airFlowType: string;                  // Flight flow type
}

/**
 * Segment information for one way flights
 */
export interface SegmentInformation {
  id: string;                           // Segment unique identifier
  FlightDesignator: FlightDesignator;   // Flight designator details
  OAC?: AirlineInfoOneWay;             // Operating Airline Code (optional)
  stops: number;                        // Number of stops
  StopOverairports: any[];             // Stop over airports
  duration: number;                     // Flight duration in minutes
  DepartureAirport: AirportInfoOneWay; // Departure airport
  ArrivalAirport: AirportInfoOneWay;   // Arrival airport
  DepartureTime: string;               // Departure time
  Arrivaltime: string;                 // Arrival time
  IsArrivingNextDay: boolean;          // Is arriving next day
  isReturnSegment: boolean;            // Is return segment
  SegmentNumber: number;               // Segment number
}

/**
 * Flight designator information
 */
export interface FlightDesignator {
  MAC: AirlineInfoOneWay;              // Marketing Airline Code
  FlightNumber: string;                // Flight number
  EquipmentType: string;               // Aircraft equipment type
}

/**
 * Airline information for one way flights
 */
export interface AirlineInfoOneWay {
  code: string;                        // Airline code
  isLcc: boolean;                      // Is Low Cost Carrier
  name: string;                        // Airline name
}

/**
 * Airport information for one way flights
 */
export interface AirportInfoOneWay {
  city: string;                        // City name
  cityCode?: string;                   // City code (optional)
  code: string;                        // Airport code
  country: string;                     // Country name
  countryCode: string;                 // Country code
  name: string;                        // Airport name
  terminal?: string;                   // Terminal information (optional)
  timezoneId?: string;                 // Timezone ID (optional)
}

/**
 * Fare option for one way flights
 */
export interface FareOptionOneWay {
  FareDetail: FareDetailsByPaxTypeOneWay;     // Fare details by passenger type
  fareIdentifier: string;                     // Unique fare identifier
  PriceId: string;                           // Price identifier
  MatchedSpecialReturnIdentifier: any[];     // Special return identifiers
  messages: any[];                           // Fare messages
  TripAdditionalInformation: TripAdditionalInformation; // Additional trip information
  icca: boolean;                             // Is cabin class available
}

/**
 * Fare details by passenger type for one way flights
 */
export interface FareDetailsByPaxTypeOneWay {
  CHILD: PassengerFareDetailsOneWay;         // Child passenger fare details
  INFANT: PassengerFareDetailsOneWay;        // Infant passenger fare details
  ADULT: PassengerFareDetailsOneWay;         // Adult passenger fare details
}

/**
 * Passenger fare details for one way flights
 */
export interface PassengerFareDetailsOneWay {
  fareComponents: FareComponentsOneWay;      // Fare components
  AdditionalFareComponents: AdditionalFareComponents; // Additional fare components
  Baggageinformation: BaggageInformation;    // Baggage information
  RefundableType: number;                    // Refundable type
  CabinClass: string;                        // Cabin class
  ClassOfBooking: string;                    // Class of booking
  FareBasis: string;                         // Fare basis
  "Seats Remaining"?: number;                // Seats remaining (key has space)
  MealIndicator?: boolean;                   // Meal indicator
  "MealIndicator "?: boolean;                // Meal indicator (key has space)
  HandBaggageindicator?: boolean;            // Hand baggage indicator
}

/**
 * Fare components for one way flights
 */
export interface FareComponentsOneWay {
  NetFare: number;                           // Net fare
  TaxesAndFees: number;                      // Taxes and fees
  "TaxesAndFees "?: number;                  // Taxes and fees (key has space for INFANT)
  BaseFare: number;                          // Base fare
  TotalFare: number;                         // Total fare
}

/**
 * Additional fare components for one way flights
 */
export interface AdditionalFareComponents {
  TaxesAndFees: TaxBreakdownOneWay;          // Tax breakdown
}

/**
 * Tax breakdown for one way flights
 */
export interface TaxBreakdownOneWay {
  FuelSurcharge?: number;                    // Fuel surcharge (optional)
  ManagementFeeTax: number;                  // Management fee tax
  OtherCharges?: number;                     // Other charges (optional)
  ManagementFee: number;                     // Management fee
  AirlineGSTComponent?: number;              // Airline GST component (optional)
  CarrierMiscFee?: number;                   // Carrier miscellaneous fee (optional)
  "FlexTotalCharges "?: number;              // Flex total charges (key has space)
}

/**
 * Baggage information for one way flights
 */
export interface BaggageInformation {
  CheckingBaggage?: string;                  // Checking baggage (optional, not on INFANT fare)
  CabinBaggage: string;                      // Cabin baggage
}

/**
 * Trip additional information for one way flights
 */
export interface TripAdditionalInformation {
  TripBaggageInformation: {
    [segmentId: string]: SegmentBaggagePerPaxOneWay[];
  };
}

/**
 * Segment baggage per passenger for one way flights
 * Models the array like [{"INFANT": {...}}, {"ADULT": {...}}]
 */
export interface SegmentBaggagePerPaxOneWay {
  INFANT?: SegmentBaggageDetails;            // Infant baggage details
  ADULT?: SegmentBaggageDetails;             // Adult baggage details
  CHILD?: SegmentBaggageDetails;             // Child baggage details
}

/**
 * Segment baggage details for one way flights
 */
export interface SegmentBaggageDetails {
  cB: string;                                // Cabin baggage
  iB?: string;                               // Included baggage (optional)
}