export interface FlightSearchPayload {
  tui: string;
  search: SearchDetails;
}

export interface SearchDetails {
  ADT: number;
  CHD: number;
  INF: number;
  Cabin: string;
  Source: string;
  Mode: string;
  ClientID: string;
  FareType: string;
  SecType: string;
  TUI: string;
  Trips: SearchTrip[];
  Parameters: SearchParameters;
}

export interface SearchTrip {
  From: string;
  To: string;
  OnwardDate: string;
  ReturnDate: string;
  TUI: string;
}

export interface SearchParameters {
  IsDirect: boolean;
  PaxCategory: string;
  Refundable: string;
}

