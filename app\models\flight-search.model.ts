// ============================================================================
// FLIGHT SEARCH REQUEST MODELS
// ============================================================================

/**
 * Main payload structure for flight search API requests
 */
export interface FlightSearchPayload {
  tui: string;
  search: SearchDetails;
}

/**
 * Detailed search parameters for flight search
 */
export interface SearchDetails {
  ADT: number;           // Adult passengers count
  CHD: number;           // Child passengers count
  INF: number;           // Infant passengers count
  Cabin: string;         // Cabin class (E=Economy, PE=Premium Economy, B=Business, F=First)
  Source: string;        // Source system identifier
  Mode: string;          // Search mode
  ClientID: string;      // Client identifier
  FareType: string;      // Fare type (ON=Online, OFF=Offline)
  SecType: string;       // Sector type (D=Domestic, I=International)
  TUI: string;           // Transaction Unique Identifier
  Trips: SearchTrip[];   // Trip details array
  Parameters: SearchParameters; // Additional search parameters
}

/**
 * Individual trip information for search
 */
export interface SearchTrip {
  From: string;          // Origin airport code
  To: string;            // Destination airport code
  OnwardDate: string;    // Departure date (YYYY-MM-DD)
  ReturnDate: string;    // Return date (YYYY-MM-DD) - empty for one-way
  TUI: string;           // Transaction Unique Identifier
}

/**
 * Additional search parameters and filters
 */
export interface SearchParameters {
  IsDirect: boolean;     // Direct flights only flag
  PaxCategory: string;   // Passenger category
  Refundable: string;    // Refundable fare preference
}

// ============================================================================
// FLIGHT SEARCH FORM DATA MODELS
// ============================================================================

/**
 * Frontend form data structure for flight search
 */
export interface FlightSearchFormData {
  from: AirportData;
  to: AirportData;
  departureDate: string;
  returnDate?: string;
  adults: number;
  children: number;
  infants: number;
  cabin: CabinClass;
  tripType: TripType;
  fareType: FareType;
  isDirect: boolean;
}

/**
 * Airport data structure for form
 */
export interface AirportData {
  code: string;
  name: string;
  city: string;
  country: string;
  countryCode: string;
}

/**
 * Cabin class options
 */
export type CabinClass = 'E' | 'PE' | 'B' | 'F';

/**
 * Trip type options
 */
export type TripType = 'oneWay' | 'roundTrip';

/**
 * Fare type options
 */
export type FareType = 'ON' | 'OFF';

// ============================================================================
// SEARCH RESPONSE MODELS
// ============================================================================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  timestamp?: string;
}

/**
 * Flight search response data
 */
export interface FlightSearchResponseData {
  searchId: string;
  tui?: string;
  totalResults: number;
  onwardFlights: FlightResult[];
  returnFlights?: FlightResult[];
  filters: SearchFilters;
}

/**
 * Individual flight result
 */
export interface FlightResult {
  id: string;
  airline: string;
  flightNumber: string;
  price: number;
  currency: string;
  duration: string;
  stops: number;
  departure: FlightPoint;
  arrival: FlightPoint;
  segments: FlightSegment[];
  fareDetails: FareDetails;
}

/**
 * Flight departure/arrival point
 */
export interface FlightPoint {
  time: string;
  airport: string;
  city: string;
  date: string;
  terminal?: string;
}

/**
 * Flight segment information
 */
export interface FlightSegment {
  id: string;
  airline: string;
  flightNumber: string;
  aircraft?: string;
  departure: FlightPoint;
  arrival: FlightPoint;
  duration: number;
  stops: number;
}

/**
 * Fare details and pricing
 */
export interface FareDetails {
  baseFare: number;
  taxes: number;
  totalFare: number;
  currency: string;
  fareClass: string;
  refundable: boolean;
  baggage: BaggageInfo;
}

/**
 * Baggage information
 */
export interface BaggageInfo {
  cabin: string;
  checkin: string;
}

/**
 * Search filters for results
 */
export interface SearchFilters {
  airlines: string[];
  priceRange: { min: number; max: number };
  durationRange: { min: number; max: number };
  stops: string[];
  departureTimeSlots: string[];
  arrivalTimeSlots: string[];
}