// ============================================================================
// FLIGHT LIST SERVICE
// ============================================================================

import { FlightSearchPayload } from "@/app/models/flight-search.model";
import apiService from "./api-service";
import {
  FlightApiResponseInternational,
  FlightApiResponseOneWay,
  FlightApiResponseRoundTripDomestic
} from "@/app/models/flight-list.model";

// ============================================================================
// FLIGHT SEARCH FUNCTIONS
// ============================================================================

/**
 * Search for one-way flights
 * @param body - Flight search payload containing search criteria
 * @returns Promise<FlightApiResponseOneWay> - One-way flight search results
 */
export const getFlightListOneWay = async (body: FlightSearchPayload): Promise<FlightApiResponseOneWay> => {
  try {
    console.log('🔍 Searching one-way flights with payload:', body);
    const response = await apiService.post<FlightApiResponseOneWay>('search', body);
    console.log('✅ One-way flight search completed successfully');
    return response;
  } catch (error) {
    console.error('❌ Error searching one-way flights:', error);
    throw error;
  }
};

/**
 * Search for round-trip international flights
 * @param body - Flight search payload containing search criteria
 * @returns Promise<FlightApiResponseInternational> - International round-trip flight search results
 */
export const getFlightListRoundTripInternational = async (body: FlightSearchPayload): Promise<FlightApiResponseInternational> => {
  try {
    console.log('🌍 Searching international round-trip flights with payload:', body);
    const response = await apiService.post<FlightApiResponseInternational>('search', body);
    console.log('✅ International round-trip flight search completed successfully');
    return response;
  } catch (error) {
    console.error('❌ Error searching international round-trip flights:', error);
    throw error;
  }
};

/**
 * Search for round-trip domestic flights
 * @param body - Flight search payload containing search criteria
 * @returns Promise<FlightApiResponseRoundTripDomestic> - Domestic round-trip flight search results
 */
export const getFlightListRoundTripDomestic = async (body: FlightSearchPayload): Promise<FlightApiResponseRoundTripDomestic> => {
  try {
    console.log('🏠 Searching domestic round-trip flights with payload:', body);
    const response = await apiService.post<FlightApiResponseRoundTripDomestic>('search', body);
    console.log('✅ Domestic round-trip flight search completed successfully');
    return response;
  } catch (error) {
    console.error('❌ Error searching domestic round-trip flights:', error);
    throw error;
  }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Determine flight search type based on route and trip type
 * @param from - Origin airport code
 * @param to - Destination airport code
 * @param tripType - Type of trip (oneWay or roundTrip)
 * @returns Flight search type identifier
 */
export const determineFlightSearchType = (from: string, to: string, tripType: 'oneWay' | 'roundTrip'): 'oneWay' | 'roundTripDomestic' | 'roundTripInternational' => {
  if (tripType === 'oneWay') {
    return 'oneWay';
  }

  // Check if route is international (simplified logic - can be enhanced)
  const indianAirports = ['DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', 'AMD', 'COK', 'GOI', 'PNQ'];
  const isFromIndian = indianAirports.includes(from);
  const isToIndian = indianAirports.includes(to);

  if (isFromIndian && isToIndian) {
    return 'roundTripDomestic';
  } else {
    return 'roundTripInternational';
  }
};

/**
 * Generic flight search function that automatically determines the correct search type
 * @param body - Flight search payload
 * @param from - Origin airport code
 * @param to - Destination airport code
 * @param tripType - Type of trip
 * @returns Promise with appropriate flight search response
 */
export const searchFlights = async (
  body: FlightSearchPayload,
  from: string,
  to: string,
  tripType: 'oneWay' | 'roundTrip'
): Promise<FlightApiResponseOneWay | FlightApiResponseRoundTripDomestic | FlightApiResponseInternational> => {
  const searchType = determineFlightSearchType(from, to, tripType);

  switch (searchType) {
    case 'oneWay':
      return await getFlightListOneWay(body);
    case 'roundTripDomestic':
      return await getFlightListRoundTripDomestic(body);
    case 'roundTripInternational':
      return await getFlightListRoundTripInternational(body);
    default:
      throw new Error(`Unsupported search type: ${searchType}`);
  }
};

// ============================================================================
// LEGACY FUNCTION ALIASES (for backward compatibility)
// ============================================================================

/**
 * @deprecated Use getFlightListRoundTripInternational instead
 */
export const getFlightListRoundTripinternational = getFlightListRoundTripInternational;